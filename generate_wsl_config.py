#!/usr/bin/env python3
"""
动态配置生成器
根据当前环境自动生成适配的配置文件
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from util.path_utils import (
    detect_environment, get_dataset_specific_paths, 
    validate_paths, print_environment_info
)

def generate_config_content(dataset_name, use_mamba_ssm=True):
    """
    生成配置文件内容
    
    Args:
        dataset_name (str): 数据集名称
        use_mamba_ssm (bool): 是否使用 mamba-ssm
        
    Returns:
        str: 配置文件内容
    """
    env = detect_environment()
    paths = get_dataset_specific_paths(dataset_name)
    
    # 基础配置
    config_lines = [
        f"# CIM-Tracker {dataset_name.upper()} 配置文件",
        f"# 自动生成，适配 {env.upper()} 环境",
        f"# 生成时间: {os.popen('date').read().strip() if env != 'windows' else 'auto-generated'}",
        "",
        "# ================== 基础模型配置 ==================",
    ]
    
    # 模型架构
    if use_mamba_ssm:
        config_lines.extend([
            "--meta_arch motr_mamba_ssm",
            "--use_mamba_ssm",
        ])
    else:
        config_lines.append("--meta_arch motr")
    
    # 数据集配置
    dataset_file_mapping = {
        'dancetrack': 'e2e_dance',
        'sportsmot': 'e2e_joint',
        'bft': 'e2e_joint'
    }
    
    config_lines.extend([
        f"--dataset_file {dataset_file_mapping.get(dataset_name, 'e2e_dance')}",
        "--epochs 20",
        "--with_box_refine",
        "--lr_drop 15",
        "--lr_scheduler cosine",
        "--lr 2e-4",
        "--lr_backbone 2e-5",
        "",
        "# ================== 预训练模型 ==================",
        f"--pretrained {paths['pretrained']}",
        "",
    ])
    
    # 性能配置
    if use_mamba_ssm:
        config_lines.extend([
            "# ================== 高性能配置 (mamba-ssm) ==================",
            "--batch_size 8",
            "--sampler_lengths 10",
        ])
    else:
        config_lines.extend([
            "# ================== 标准配置 ==================", 
            "--batch_size 4",
            "--sampler_lengths 5",
        ])
    
    config_lines.extend([
        "--sample_mode random_interval",
        "--sample_interval 5",
        "--dropout 0.1",
        "",
        "# ================== CIM-Tracker 特有参数 ==================",
    ])
    
    # Mamba 参数 (根据数据集调整)
    mamba_params = {
        'dancetrack': {
            'layers': 3, 'state_dim': 12, 'expand': 1.5, 'conv_dim': 4,
            'queries': 8, 'id_vocab': 50
        },
        'sportsmot': {
            'layers': 4, 'state_dim': 16, 'expand': 2, 'conv_dim': 4,
            'queries': 30, 'id_vocab': 100
        },
        'bft': {
            'layers': 4, 'state_dim': 16, 'expand': 2, 'conv_dim': 4,
            'queries': 50, 'id_vocab': 150
        }
    }
    
    params = mamba_params.get(dataset_name, mamba_params['dancetrack'])
    
    config_lines.extend([
        f"--mamba_num_layers {params['layers']}",
        f"--mamba_state_dim {params['state_dim']}",
        f"--mamba_expand {params['expand']}",
        f"--mamba_conv_dim {params['conv_dim']}",
        "--id_dim 256",
        "",
        "# ================== ID 学习参数 ==================",
        f"--num_id_vocabulary {params['id_vocab']}",
        "--id_loss_coef 2.0",
        "",
        "# ================== 轨迹增强参数 ==================",
        "--aug_occlusion_prob 0.1",
        "--aug_switch_prob 0.1", 
        "--mask_obs_threshold 0.5",
        "",
        "# ================== 高效训练参数 ==================",
        "--grad_frames 3",
        "",
        "# ================== 查询和检测参数 ==================",
        f"--num_queries {params['queries']}",
        "--query_denoise 0.1",
        "",
        "# ================== 损失函数权重 ==================",
        "--cls_loss_coef 2.0",
        "--bbox_loss_coef 5.0",
        "--giou_loss_coef 2.0",
        "",
        "# ================== 数据集路径配置 ==================",
        f"--mot_path {paths['mot_path']}",
        f"--det_db {paths['det_db']}",
        "",
        "# ================== 训练优化配置 ==================",
        "--use_checkpoint",
        "--use_amp",
    ])
    
    # 编译优化 (Linux/WSL 支持更好)
    if env in ['linux', 'wsl']:
        config_lines.extend([
            "--compile_model",
            "--compile_mode reduce-overhead",
            "--num_workers 4",
        ])
    else:
        config_lines.extend([
            "# --compile_model  # Windows 兼容性问题",
            "# --compile_mode reduce-overhead",
            "--num_workers 0",
        ])
    
    config_lines.extend([
        "",
        "# ================== 输出配置 ==================",
        f"--output_dir {paths['output_dir']}",
    ])
    
    return "\n".join(config_lines)

def generate_config_file(dataset_name, use_mamba_ssm=True, output_dir="configs"):
    """
    生成配置文件
    
    Args:
        dataset_name (str): 数据集名称
        use_mamba_ssm (bool): 是否使用 mamba-ssm
        output_dir (str): 输出目录
        
    Returns:
        str: 生成的配置文件路径
    """
    env = detect_environment()
    
    # 生成文件名
    if use_mamba_ssm:
        filename = f"cim_tracker_{dataset_name}_{env}_mamba_ssm.args"
    else:
        filename = f"cim_tracker_{dataset_name}_{env}.args"
    
    filepath = os.path.join(output_dir, filename)
    
    # 生成内容
    content = generate_config_content(dataset_name, use_mamba_ssm)
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 写入文件
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 配置文件已生成: {filepath}")
    return filepath

def validate_generated_config(config_path):
    """
    验证生成的配置文件
    
    Args:
        config_path (str): 配置文件路径
        
    Returns:
        bool: 验证是否通过
    """
    print(f"\n🔍 验证配置文件: {config_path}")
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    # 读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 检查关键参数
    required_params = [
        '--meta_arch', '--dataset_file', '--pretrained', 
        '--mot_path', '--det_db', '--output_dir'
    ]
    
    found_params = set()
    path_params = {}
    
    for line in lines:
        line = line.strip()
        if line.startswith('--'):
            parts = line.split(' ', 1)
            if len(parts) >= 1:
                param = parts[0]
                found_params.add(param)
                
                # 检查路径参数
                if param in ['--pretrained', '--mot_path', '--det_db'] and len(parts) == 2:
                    path_params[param] = parts[1]
    
    # 检查必需参数
    missing_params = set(required_params) - found_params
    if missing_params:
        print(f"❌ 缺少必需参数: {missing_params}")
        return False
    
    print("✅ 所有必需参数都存在")
    
    # 检查路径是否存在
    path_issues = []
    for param, path in path_params.items():
        if not os.path.exists(path):
            path_issues.append(f"{param}: {path}")
    
    if path_issues:
        print("⚠️  以下路径不存在:")
        for issue in path_issues:
            print(f"   - {issue}")
        print("   请确保数据和模型文件在正确位置")
    else:
        print("✅ 所有路径都存在")
    
    return len(path_issues) == 0

def main():
    """主函数"""
    print("🔧 CIM-Tracker 动态配置生成器")
    print("="*50)
    
    # 打印环境信息
    print_environment_info()
    
    # 支持的数据集
    datasets = ['dancetrack', 'sportsmot', 'bft']
    
    # 生成配置文件
    generated_configs = []
    
    for dataset in datasets:
        print(f"\n📊 生成 {dataset.upper()} 配置...")
        
        # 验证路径
        validation = validate_paths(dataset)
        missing_paths = [k for k, v in validation.items() if not v and k != 'output_dir']
        
        if missing_paths:
            print(f"⚠️  {dataset} 缺少以下路径: {missing_paths}")
            print(f"   跳过 {dataset} 配置生成")
            continue
        
        # 生成标准版本
        config_path = generate_config_file(dataset, use_mamba_ssm=False)
        generated_configs.append(config_path)
        
        # 生成 mamba-ssm 版本
        config_path_ssm = generate_config_file(dataset, use_mamba_ssm=True)
        generated_configs.append(config_path_ssm)
    
    # 验证生成的配置
    print("\n🔍 验证生成的配置文件...")
    for config_path in generated_configs:
        validate_generated_config(config_path)
    
    # 总结
    print(f"\n🎉 配置生成完成！")
    print(f"   - 生成了 {len(generated_configs)} 个配置文件")
    print(f"   - 位置: configs/ 目录")
    
    env = detect_environment()
    print(f"\n💡 使用建议:")
    if env == 'wsl':
        print("   - 推荐使用 mamba-ssm 版本获得最佳性能")
        print("   - 示例: python3 main.py configs/cim_tracker_dancetrack_wsl_mamba_ssm.args")
    elif env == 'windows':
        print("   - 使用标准版本 (mamba-ssm 在 Windows 不可用)")
        print("   - 示例: python main.py configs/cim_tracker_dancetrack_windows.args")
    else:
        print("   - 可以使用任一版本")
        print("   - 推荐 mamba-ssm 版本获得更好性能")

if __name__ == '__main__':
    main()
