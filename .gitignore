# Python 缓存和编译文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 模型权重和训练输出
*.pth
*.train
*.ckpt
*.weights
exps/
outputs/
logs/
checkpoints/
runs/

# 构建和分发文件
build/
dist/
*.egg
*.egg-info/
.eggs/

# 数据文件
*.mp4
*.avi
*.mov
*.jpg
*.jpeg
*.png
*.txt
*.json
data/
datasets/
*.zip
*.tar.gz

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 环境和配置
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# 测试和覆盖率
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# 临时文件
*.tmp
*.temp
*.log
*.out
*.err

# 开发和测试文件
test_*.py
*_test.py
debug_*.py
cleanup_*.py

# 实验和调试输出
visualize_tmp/
debug_outputs/
temp_results/
output/
wandb/
models/ops/