# 文件: models/cdiim_mamba_ssm.py
# 基于官方 mamba-ssm 的高性能 CDIIM 实现
# 这是一个全新的实现，专门为 WSL/Linux 环境设计

import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange

try:
    from mamba_ssm import Mamba
    MAMBA_SSM_AVAILABLE = True
    print("✅ mamba-ssm 可用，将使用高性能实现")
except ImportError:
    MAMBA_SSM_AVAILABLE = False
    print("❌ mamba-ssm 不可用，请在 WSL/Linux 环境中安装")
    print("   安装命令: pip install mamba-ssm")

from .deformable_detr import MLP  # 复用MOTR中的MLP


class MambaStateSynchronizerV2(nn.Module):
    """
    优化版状态同步器，适配 mamba-ssm 的输出
    使用高效的自注意力机制进行轨迹间信息交互
    """
    def __init__(self, d_model, n_head=8):
        super().__init__()
        self.d_model = d_model
        self.n_head = n_head
        
        # 使用标准的多头自注意力
        self.self_attn = nn.MultiheadAttention(
            d_model, n_head, batch_first=True, dropout=0.1
        )
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model * 2, d_model),
            nn.Dropout(0.1)
        )
    
    def forward(self, x):
        """
        输入: x (B, K, D) - B个batch，K个轨迹，D维特征
        输出: synced_x (B, K, D) - 同步后的特征
        """
        B, K, D = x.shape
        
        # 自注意力进行轨迹间信息交互
        attn_out, _ = self.self_attn(x, x, x)
        x = self.norm1(x + attn_out)
        
        # 前馈网络
        ffn_out = self.ffn(x)
        x = self.norm2(x + ffn_out)
        
        return x


class CDIIMv2(nn.Module):
    """
    基于官方 mamba-ssm 的高性能 CDIIM 实现
    
    主要改进：
    1. 使用官方 mamba-ssm 包的 Mamba 类，获得 CUDA 优化
    2. 并行处理整个序列，消除逐帧循环
    3. 优化的状态同步机制
    4. 更高效的内存使用
    """
    
    def __init__(self, d_model=256, n_head=8, num_mamba_layers=4, 
                 mamba_state_dim=16, mamba_expand=2, mamba_conv_dim=4,
                 num_id_vocabulary=50, id_dim=256):
        super().__init__()
        
        if not MAMBA_SSM_AVAILABLE:
            raise ImportError(
                "mamba-ssm 不可用。请在 WSL/Linux 环境中安装:\n"
                "pip install mamba-ssm\n"
                "或者使用原始的 CDIIM 实现"
            )
        
        self.d_model = d_model
        self.num_mamba_layers = num_mamba_layers
        
        # ================== 核心 Mamba 模块 ==================
        # 使用官方 mamba-ssm 的 Mamba 类
        self.track_mamba = Mamba(
            d_model=d_model,
            d_state=mamba_state_dim,    # 对应 n 参数
            d_conv=mamba_conv_dim,      # 卷积核大小
            expand=mamba_expand,        # 扩展因子 e
            # 注意：mamba-ssm 内部处理多层，但我们可能需要多个实例
        )
        
        # 如果需要多层，创建多个 Mamba 实例
        if num_mamba_layers > 1:
            self.mamba_layers = nn.ModuleList([
                Mamba(
                    d_model=d_model,
                    d_state=mamba_state_dim,
                    d_conv=mamba_conv_dim,
                    expand=mamba_expand,
                ) for _ in range(num_mamba_layers)
            ])
        else:
            self.mamba_layers = nn.ModuleList([self.track_mamba])
        
        # ================== 状态同步模块 ==================
        self.synchronizer = MambaStateSynchronizerV2(d_model, n_head)
        
        # ================== 预测头 ==================
        # 边界框预测头
        self.bbox_head = MLP(d_model, d_model, 4, 3)
        
        # ID 预测头
        self.id_pred_head = MLP(d_model, d_model, num_id_vocabulary, 3)
        
        # ================== 可学习的 ID 嵌入字典 ==================
        self.id_embedding = nn.Embedding(num_id_vocabulary, id_dim)
        
        # ================== 层归一化 ==================
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(num_mamba_layers)
        ])
        
        print(f"✅ CDIIMv2 初始化完成:")
        print(f"   - Mamba 层数: {num_mamba_layers}")
        print(f"   - 模型维度: {d_model}")
        print(f"   - 状态维度: {mamba_state_dim}")
        print(f"   - ID 词汇量: {num_id_vocabulary}")
    
    def forward(self, track_instances):
        """
        高性能前向传播，使用官方 mamba-ssm 进行并行处理
        
        输入:
            track_instances: 包含轨迹信息的对象
            
        输出:
            track_instances: 更新后的轨迹实例
            pred_boxes: 预测的边界框 (B, K, T, 4)
            pred_logits: 预测的ID logits (B, K, T, num_classes)
        """
        # 1. 特征融合 (假设已经通过 CIFE 处理)
        # 这里需要从 track_instances 中提取特征
        # 形状应该是 (B, K, T, D)
        
        # 获取输入特征 (这部分需要根据实际的 track_instances 结构调整)
        if hasattr(track_instances, 'query_embed'):
            # 假设 query_embed 包含了融合后的特征
            fused_features = track_instances.query_embed
        else:
            # 如果没有预处理的特征，需要先进行特征提取
            raise NotImplementedError("需要先通过 CIFE 模块提取特征")
        
        B, K, T, D = fused_features.shape
        
        # 2. 重塑输入以适配 Mamba
        # mamba-ssm 期望输入形状为 (batch, length, dim)
        # 我们将 B*K 作为 batch 维度，T 作为序列长度
        mamba_input = fused_features.view(B * K, T, D)
        
        # 3. 通过多层 Mamba 进行序列建模
        mamba_output = mamba_input
        
        for i, (mamba_layer, norm) in enumerate(zip(self.mamba_layers, self.layer_norms)):
            # Mamba 前向传播 (并行处理整个序列)
            layer_output = mamba_layer(mamba_output)
            
            # 残差连接和层归一化
            mamba_output = norm(mamba_output + layer_output)
            
            # 可选：在每层之后进行状态同步
            if i < len(self.mamba_layers) - 1:  # 不在最后一层进行同步
                # 重塑为 (B, K, T, D) 进行同步
                sync_input = mamba_output.view(B, K, T, D)
                
                # 对每个时间步进行同步
                synced_output = []
                for t in range(T):
                    step_features = sync_input[:, :, t, :]  # (B, K, D)
                    synced_step = self.synchronizer(step_features)  # (B, K, D)
                    synced_output.append(synced_step)
                
                synced_output = torch.stack(synced_output, dim=2)  # (B, K, T, D)
                mamba_output = synced_output.view(B * K, T, D)
        
        # 4. 最终输出重塑
        final_output = mamba_output.view(B, K, T, D)
        
        # 5. 最终状态同步 (在所有时间步上)
        # 对最后一个时间步进行全局同步
        final_step_features = final_output[:, :, -1, :]  # (B, K, D)
        synced_final = self.synchronizer(final_step_features)  # (B, K, D)
        
        # 6. 预测头
        # 对整个序列进行预测
        flat_output = final_output.view(B * K * T, D)
        
        # 边界框预测
        pred_boxes = self.bbox_head(flat_output).view(B, K, T, 4)
        
        # ID 预测
        pred_id_logits = self.id_pred_head(flat_output).view(B, K, T, -1)
        
        # 7. 更新 track_instances (这部分需要根据具体需求实现)
        # 通常只使用最后一个时间步的预测来更新轨迹
        if hasattr(track_instances, 'pred_boxes'):
            track_instances.pred_boxes = pred_boxes[:, :, -1, :]  # (B, K, 4)
        if hasattr(track_instances, 'pred_logits'):
            track_instances.pred_logits = pred_id_logits[:, :, -1, :]  # (B, K, num_classes)
        
        return track_instances, pred_boxes, pred_id_logits
    
    def get_memory_usage(self):
        """获取模型的内存使用情况"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'memory_mb': total_params * 4 / 1024 / 1024  # 假设 float32
        }


def create_cdiim_v2(args):
    """
    创建 CDIIMv2 实例的工厂函数
    """
    return CDIIMv2(
        d_model=args.hidden_dim,
        n_head=args.nheads,
        num_mamba_layers=args.mamba_num_layers,
        mamba_state_dim=args.mamba_state_dim,
        mamba_expand=args.mamba_expand,
        mamba_conv_dim=args.mamba_conv_dim,
        num_id_vocabulary=args.num_id_vocabulary,
        id_dim=args.id_dim
    )


# 兼容性检查函数
def check_mamba_ssm_compatibility():
    """
    检查 mamba-ssm 的兼容性和性能
    """
    if not MAMBA_SSM_AVAILABLE:
        print("❌ mamba-ssm 不可用")
        return False
    
    try:
        # 创建一个小的测试实例
        test_mamba = Mamba(d_model=256, d_state=16, d_conv=4, expand=2)
        
        # 测试前向传播
        test_input = torch.randn(2, 10, 256)  # (batch, seq_len, dim)
        with torch.no_grad():
            test_output = test_mamba(test_input)
        
        print("✅ mamba-ssm 兼容性测试通过")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {test_output.shape}")
        return True
        
    except Exception as e:
        print(f"❌ mamba-ssm 兼容性测试失败: {e}")
        return False


if __name__ == "__main__":
    # 运行兼容性检查
    check_mamba_ssm_compatibility()
