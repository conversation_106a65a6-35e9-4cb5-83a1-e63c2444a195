# 文件: models/criterion.py
# CIPT 损失准则 - 第3步实现

import torch
import torch.nn.functional as F
from torch import nn
from util.box_ops import box_cxcywh_to_xyxy, generalized_box_iou


class SetCriterion(nn.Module):
    """
    CIPT 的损失准则
    
    基于匈牙利匹配的二分图匹配机制，包含：
    1. 分配损失 (交叉熵)
    2. 边界框回归损失 (L1 + GIoU)
    """
    
    def __init__(self, num_classes, matcher, weight_dict, losses):
        super().__init__()
        self.num_classes = num_classes  # 这里的num_classes是轨迹槽位数量
        self.matcher = matcher
        self.weight_dict = weight_dict
        self.losses = losses
        
        # ID分配的分类损失
        self.loss_labels = nn.CrossEntropyLoss()

    def _get_src_permutation_idx(self, indices):
        """获取源索引的排列"""
        batch_idx = torch.cat([torch.full_like(src, i) for i, (src, _) in enumerate(indices)])
        src_idx = torch.cat([src for (src, _) in indices])
        return batch_idx, src_idx

    def _get_tgt_permutation_idx(self, indices):
        """获取目标索引的排列"""
        batch_idx = torch.cat([torch.full_like(tgt, i) for i, (_, tgt) in enumerate(indices)])
        tgt_idx = torch.cat([tgt for (_, tgt) in indices])
        return batch_idx, tgt_idx

    def get_loss(self, loss, outputs, targets, indices, num_boxes):
        """根据损失类型调用相应的损失函数"""
        loss_map = {
            'labels': self.loss_labels_func,
            'boxes': self.loss_boxes
        }
        return loss_map[loss](outputs, targets, indices, num_boxes)

    def loss_labels_func(self, outputs, targets, indices, num_boxes, log=True):
        """
        分类损失 - 预测分配到哪个轨迹槽位

        outputs['pred_logits'] 的维度应为 [batch_size, num_track_slots, num_track_slots + 1]
        我们要预测的是分配到哪个槽位
        """
        assert 'pred_logits' in outputs
        src_logits = outputs['pred_logits']

        idx = self._get_src_permutation_idx(indices)

        # 处理目标类别 - 使用labels而不是track_ids
        target_classes_list = []
        for t, (_, J) in zip(targets, indices):
            if len(J) > 0 and "labels" in t and len(t["labels"]) > 0:
                target_classes_list.append(t["labels"][J])

        if target_classes_list:
            target_classes_o = torch.cat(target_classes_list)
        else:
            # 如果没有有效目标，创建空张量
            target_classes_o = torch.tensor([], dtype=torch.long, device=src_logits.device)

        target_classes = torch.full(src_logits.shape[:2], self.num_classes,
                                    dtype=torch.int64, device=src_logits.device)

        if len(target_classes_o) > 0 and len(idx[0]) > 0:
            target_classes[idx] = target_classes_o

        # 预测的是分配到哪个槽位，所以类别总数是 num_track_slots + 1 (1个代表'no object')
        loss_ce = F.cross_entropy(src_logits.transpose(1, 2), target_classes)
        losses = {'loss_ce': loss_ce}
        return losses

    def loss_boxes(self, outputs, targets, indices, num_boxes):
        """边界框回归损失"""
        assert 'pred_boxes' in outputs
        idx = self._get_src_permutation_idx(indices)
        src_boxes = outputs['pred_boxes'][idx]
        target_boxes = torch.cat([t['boxes'][i] for t, (_, i) in zip(targets, indices)], dim=0)

        loss_bbox = F.l1_loss(src_boxes, target_boxes, reduction='none')
        losses = {}
        losses['loss_bbox'] = loss_bbox.sum() / num_boxes

        loss_giou = 1 - torch.diag(generalized_box_iou(
            box_cxcywh_to_xyxy(src_boxes),
            box_cxcywh_to_xyxy(target_boxes)))
        losses['loss_giou'] = loss_giou.sum() / num_boxes
        return losses

    def forward(self, outputs, targets):
        """
        前向传播 - 计算所有损失

        Args:
            outputs: 模型输出字典
            targets: 目标列表

        Returns:
            损失字典
        """
        # 转换目标格式以适配matcher
        converted_targets = []
        for gt_instances in targets:
            if hasattr(gt_instances, 'labels') and hasattr(gt_instances, 'boxes'):
                # 已经是正确格式
                converted_targets.append({
                    'labels': gt_instances.labels,
                    'boxes': gt_instances.boxes
                })
            else:
                # 创建虚拟目标以避免错误
                device = next(iter(outputs.values())).device
                converted_targets.append({
                    'labels': torch.tensor([], dtype=torch.long, device=device),
                    'boxes': torch.empty((0, 4), device=device)
                })

        # 使用matcher找到最优匹配
        try:
            indices = self.matcher(outputs, converted_targets)
        except Exception as e:
            print(f"⚠️  SetCriterion matcher error: {e}")
            # 创建空的匹配结果
            batch_size = outputs['pred_logits'].shape[0]
            indices = [(torch.tensor([], dtype=torch.long), torch.tensor([], dtype=torch.long)) for _ in range(batch_size)]

        # 计算所有目标的总数，用于归一化 - 使用转换后的targets
        num_boxes = sum(len(t["labels"]) for t in converted_targets)
        num_boxes = torch.as_tensor([num_boxes], dtype=torch.float, device=next(iter(outputs.values())).device)
        
        # 避免除零
        if num_boxes == 0:
            num_boxes = torch.as_tensor([1.0], dtype=torch.float, device=next(iter(outputs.values())).device)
        
        losses = {}
        for loss in self.losses:
            losses.update(self.get_loss(loss, outputs, converted_targets, indices, num_boxes))

        return losses
