#!/bin/bash
# CIM-Tracker WSL mamba-ssm 训练启动脚本
# 专门为 WSL 环境设计

set -e  # 遇到错误立即退出

echo "🚀 CIM-Tracker WSL mamba-ssm 训练启动器"
echo "="*50

# ================== 环境检查 ==================
echo "🔍 检查环境..."

# 检查是否在 WSL 中
if ! grep -qi microsoft /proc/version 2>/dev/null; then
    echo "⚠️  警告: 似乎不在 WSL 环境中"
    echo "   当前环境: $(uname -a)"
    echo "   继续执行..."
fi

# 检查 Python 环境
if [ -d "venv_mamba_ssm" ]; then
    echo "✅ 发现虚拟环境: venv_mamba_ssm"
    source venv_mamba_ssm/bin/activate
    echo "✅ 虚拟环境已激活"
else
    echo "⚠️  未发现虚拟环境，使用系统 Python"
fi

# 检查 Python 和依赖
echo "🐍 Python 版本: $(python3 --version)"
echo "🔥 PyTorch 版本: $(python3 -c 'import torch; print(torch.__version__)' 2>/dev/null || echo '未安装')"

# 检查 CUDA
if command -v nvidia-smi &> /dev/null; then
    echo "🎮 GPU 状态:"
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits | head -1
else
    echo "❌ 未检测到 NVIDIA GPU"
fi

# 检查 mamba-ssm
echo "🐍 检查 mamba-ssm..."
if python3 -c "import mamba_ssm; print('✅ mamba-ssm 可用')" 2>/dev/null; then
    echo "✅ mamba-ssm 已安装"
else
    echo "❌ mamba-ssm 未安装"
    echo "   请运行: pip install mamba-ssm"
    exit 1
fi

# ================== 路径检查 ==================
echo "📁 检查数据路径..."

DATA_PATH="/mnt/d/Projects/Datasets"
MODEL_PATH="/mnt/d/Projects/Datasets/CIM_models"

if [ -d "$DATA_PATH" ]; then
    echo "✅ 数据路径存在: $DATA_PATH"
    echo "   - DanceTrack: $([ -d "$DATA_PATH/DanceTrack" ] && echo '✅' || echo '❌')"
    echo "   - SportsMOT: $([ -d "$DATA_PATH/SportsMOT" ] && echo '✅' || echo '❌')"
    echo "   - BFT: $([ -d "$DATA_PATH/BFT" ] && echo '✅' || echo '❌')"
else
    echo "❌ 数据路径不存在: $DATA_PATH"
    echo "   请确保 Windows D: 盘已正确挂载到 /mnt/d/"
    exit 1
fi

if [ -d "$MODEL_PATH" ]; then
    echo "✅ 模型路径存在: $MODEL_PATH"
    echo "   - 预训练权重: $(ls "$MODEL_PATH"/*.pth 2>/dev/null | wc -l) 个"
    echo "   - 检测数据库: $([ -f "$MODEL_PATH/det_db_motrv2.json" ] && echo '✅' || echo '❌')"
else
    echo "❌ 模型路径不存在: $MODEL_PATH"
    exit 1
fi

# ================== 配置选择 ==================
echo ""
echo "📊 选择训练配置:"
echo "1. DanceTrack (推荐开始) - 轻量级，训练快"
echo "2. SportsMOT - 中等复杂度"
echo "3. BFT - 最复杂，训练时间长"
echo ""

# 读取用户选择
read -p "请选择 (1-3, 默认1): " choice
choice=${choice:-1}

case $choice in
    1)
        CONFIG="configs/cim_tracker_dancetrack_wsl_mamba_ssm.args"
        DATASET="DanceTrack"
        ;;
    2)
        CONFIG="configs/cim_tracker_sportsmot_wsl_mamba_ssm.args"
        DATASET="SportsMOT"
        ;;
    3)
        CONFIG="configs/cim_tracker_bft_wsl_mamba_ssm.args"
        DATASET="BFT"
        ;;
    *)
        echo "❌ 无效选择，使用默认配置 (DanceTrack)"
        CONFIG="configs/cim_tracker_dancetrack_wsl_mamba_ssm.args"
        DATASET="DanceTrack"
        ;;
esac

echo "✅ 选择的配置: $CONFIG"
echo "✅ 数据集: $DATASET"

# 检查配置文件是否存在
if [ ! -f "$CONFIG" ]; then
    echo "❌ 配置文件不存在: $CONFIG"
    echo "   请运行: python3 generate_wsl_config.py"
    exit 1
fi

# ================== 环境变量设置 ==================
echo ""
echo "🔧 设置环境变量..."

export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH=$PYTHONPATH:$(pwd)
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4

echo "✅ 环境变量已设置"

# ================== 创建输出目录 ==================
OUTPUT_DIR=$(grep "output_dir" "$CONFIG" | cut -d' ' -f2)
mkdir -p "$OUTPUT_DIR"
echo "✅ 输出目录: $OUTPUT_DIR"

# ================== 启动训练 ==================
echo ""
echo "🚀 启动 CIM-Tracker mamba-ssm 训练..."
echo "   - 数据集: $DATASET"
echo "   - 配置文件: $CONFIG"
echo "   - 输出目录: $OUTPUT_DIR"
echo ""
echo "💡 提示:"
echo "   - 使用 Ctrl+C 停止训练"
echo "   - 训练日志保存在: $OUTPUT_DIR/"
echo "   - 监控 GPU: watch -n 1 nvidia-smi"
echo ""

# 倒计时
for i in {3..1}; do
    echo "开始训练倒计时: $i"
    sleep 1
done

echo "🎯 开始训练!"

# 启动训练
python3 main.py "$CONFIG"

echo ""
echo "🎉 训练完成!"
echo "📊 结果保存在: $OUTPUT_DIR/"
