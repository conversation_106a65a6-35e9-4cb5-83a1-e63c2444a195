# 文件: util/path_utils.py
# 路径工具函数，自动适配 Windows/WSL 环境

import os
import platform
from pathlib import Path

def detect_environment():
    """
    检测当前运行环境
    
    Returns:
        str: 'windows', 'wsl', 'linux'
    """
    # 检查是否在 WSL 中
    try:
        with open('/proc/version', 'r') as f:
            version_info = f.read().lower()
            if 'microsoft' in version_info or 'wsl' in version_info:
                return 'wsl'
    except FileNotFoundError:
        pass
    
    # 检查操作系统
    system = platform.system().lower()
    if system == 'windows':
        return 'windows'
    elif system == 'linux':
        return 'linux'
    else:
        return 'unknown'

def get_base_data_path():
    """
    根据环境返回基础数据路径
    
    Returns:
        str: 基础数据路径
    """
    env = detect_environment()
    
    if env == 'wsl':
        # WSL 环境，使用 Windows 分区挂载点
        base_path = '/mnt/d/Projects/Datasets'
        if os.path.exists(base_path):
            return base_path
        # 备选路径
        alt_paths = ['/data/Datasets', './datasets']
        for path in alt_paths:
            if os.path.exists(path):
                return path
        return '/mnt/d/Projects/Datasets'  # 默认返回
        
    elif env == 'windows':
        # Windows 环境
        base_path = 'D:/Projects/Datasets'
        if os.path.exists(base_path):
            return base_path
        # 备选路径
        alt_paths = ['./datasets', 'C:/datasets']
        for path in alt_paths:
            if os.path.exists(path):
                return path
        return 'D:/Projects/Datasets'  # 默认返回
        
    else:
        # Linux 环境
        base_path = '/data/Datasets'
        if os.path.exists(base_path):
            return base_path
        # 备选路径
        alt_paths = ['./datasets', '/home/<USER>']
        for path in alt_paths:
            if os.path.exists(path):
                return path
        return '/data/Datasets'  # 默认返回

def get_model_cache_path():
    """
    根据环境返回模型缓存路径
    
    Returns:
        str: 模型缓存路径
    """
    env = detect_environment()
    
    if env == 'wsl':
        # WSL 环境
        cache_path = '/mnt/d/Projects/Datasets/CIM_models'
        if os.path.exists(cache_path):
            return cache_path
        # 备选路径
        alt_paths = ['/data/CIM_models', './models_cache']
        for path in alt_paths:
            if os.path.exists(path):
                return path
        return '/mnt/d/Projects/Datasets/CIM_models'  # 默认返回
        
    elif env == 'windows':
        # Windows 环境
        cache_path = 'D:/Projects/Datasets/CIM_models'
        if os.path.exists(cache_path):
            return cache_path
        # 备选路径
        alt_paths = ['./models_cache', 'C:/models_cache']
        for path in alt_paths:
            if os.path.exists(path):
                return path
        return 'D:/Projects/Datasets/CIM_models'  # 默认返回
        
    else:
        # Linux 环境
        cache_path = '/data/CIM_models'
        if os.path.exists(cache_path):
            return cache_path
        # 备选路径
        alt_paths = ['./models_cache', '/home/<USER>']
        for path in alt_paths:
            if os.path.exists(path):
                return path
        return '/data/CIM_models'  # 默认返回

def get_pretrained_model_path(model_name):
    """
    获取预训练模型的完整路径
    
    Args:
        model_name (str): 模型文件名
        
    Returns:
        str: 完整的模型路径
    """
    base_path = get_model_cache_path()
    return os.path.join(base_path, model_name)

def get_detection_db_path():
    """
    获取检测数据库路径
    
    Returns:
        str: 检测数据库路径
    """
    base_path = get_model_cache_path()
    return os.path.join(base_path, 'det_db_motrv2.json')

def get_output_dir(experiment_name):
    """
    获取输出目录路径
    
    Args:
        experiment_name (str): 实验名称
        
    Returns:
        str: 输出目录路径
    """
    return f"outputs/{experiment_name}"

def normalize_path(path):
    """
    标准化路径格式
    
    Args:
        path (str): 原始路径
        
    Returns:
        str: 标准化后的路径
    """
    env = detect_environment()
    
    if env == 'wsl':
        # 将 Windows 路径转换为 WSL 路径
        if path.startswith('D:/') or path.startswith('D:\\'):
            path = path.replace('D:/', '/mnt/d/').replace('D:\\', '/mnt/d/')
            path = path.replace('\\', '/')
        elif path.startswith('C:/') or path.startswith('C:\\'):
            path = path.replace('C:/', '/mnt/c/').replace('C:\\', '/mnt/c/')
            path = path.replace('\\', '/')
    elif env == 'windows':
        # 将 Unix 路径转换为 Windows 路径
        if path.startswith('/mnt/d/'):
            path = path.replace('/mnt/d/', 'D:/')
        elif path.startswith('/mnt/c/'):
            path = path.replace('/mnt/c/', 'C:/')
        path = path.replace('/', '\\')
    
    return path

def create_directories():
    """
    创建必要的目录结构
    """
    directories = [
        get_base_data_path(),
        get_model_cache_path(),
        'outputs',
        'logs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 确保目录存在: {directory}")

def print_environment_info():
    """
    打印环境信息
    """
    env = detect_environment()
    base_data = get_base_data_path()
    model_cache = get_model_cache_path()
    
    print("🌍 环境信息:")
    print(f"   - 运行环境: {env}")
    print(f"   - 数据路径: {base_data}")
    print(f"   - 模型缓存: {model_cache}")
    print(f"   - 数据路径存在: {os.path.exists(base_data)}")
    print(f"   - 模型缓存存在: {os.path.exists(model_cache)}")

def get_dataset_specific_paths(dataset_name):
    """
    获取特定数据集的路径配置
    
    Args:
        dataset_name (str): 数据集名称 ('dancetrack', 'sportsmot', 'bft')
        
    Returns:
        dict: 包含所有路径的字典
    """
    base_data = get_base_data_path()
    model_cache = get_model_cache_path()
    
    # 数据集名称映射
    dataset_mapping = {
        'dancetrack': 'DanceTrack',
        'sportsmot': 'SportsMOT', 
        'bft': 'BFT'
    }
    
    # 预训练模型映射
    pretrained_mapping = {
        'dancetrack': 'r50_deformable_detr_coco_dancetrack.pth',
        'sportsmot': 'r50_deformable_detr_coco_sportsmot.pth',
        'bft': 'r50_deformable_detr_coco_bft.pth'
    }
    
    dataset_dir = dataset_mapping.get(dataset_name, dataset_name)
    pretrained_file = pretrained_mapping.get(dataset_name, 'r50_deformable_detr_coco_dancetrack.pth')
    
    return {
        'mot_path': base_data,
        'dataset_path': os.path.join(base_data, dataset_dir),
        'pretrained': os.path.join(model_cache, pretrained_file),
        'det_db': os.path.join(model_cache, 'det_db_motrv2.json'),
        'resnet_weights': os.path.join(model_cache, 'resnet50-0676ba61.pth'),
        'output_dir': f"outputs/cim_tracker_{dataset_name}",
        'model_cache': model_cache
    }

def validate_paths(dataset_name):
    """
    验证路径是否存在
    
    Args:
        dataset_name (str): 数据集名称
        
    Returns:
        dict: 验证结果
    """
    paths = get_dataset_specific_paths(dataset_name)
    results = {}
    
    for key, path in paths.items():
        if key == 'output_dir':
            # 输出目录可以不存在，会自动创建
            results[key] = True
        else:
            results[key] = os.path.exists(path)
    
    return results

if __name__ == '__main__':
    # 测试函数
    print_environment_info()
    create_directories()
    
    # 测试数据集路径
    for dataset in ['dancetrack', 'sportsmot', 'bft']:
        print(f"\n📊 {dataset.upper()} 路径配置:")
        paths = get_dataset_specific_paths(dataset)
        validation = validate_paths(dataset)
        
        for key, path in paths.items():
            status = "✅" if validation[key] else "❌"
            print(f"   - {key}: {path} {status}")
