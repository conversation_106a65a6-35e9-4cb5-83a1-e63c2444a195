#!/usr/bin/env python3
"""
CIM-Tracker mamba-ssm 兼容性检查脚本
用于验证环境是否正确配置
"""

import sys
import torch
import platform

def check_system():
    """检查系统环境"""
    print("🖥️  系统信息:")
    print(f"   - 操作系统: {platform.system()} {platform.release()}")
    print(f"   - Python 版本: {sys.version}")
    print(f"   - PyTorch 版本: {torch.__version__}")
    
    # 检查是否在 WSL 中
    try:
        with open('/proc/version', 'r') as f:
            version_info = f.read()
            if 'microsoft' in version_info.lower() or 'wsl' in version_info.lower():
                print("   - 环境: WSL ✅")
                return True
            else:
                print("   - 环境: Linux ✅")
                return True
    except FileNotFoundError:
        if platform.system() == "Linux":
            print("   - 环境: Linux ✅")
            return True
        else:
            print("   - 环境: Windows ❌")
            print("     ⚠️  mamba-ssm 需要 Linux/WSL 环境")
            return False

def check_cuda():
    """检查 CUDA 环境"""
    print("\n🎮 CUDA 环境:")
    
    if not torch.cuda.is_available():
        print("   - CUDA 可用: ❌")
        print("     ⚠️  需要 NVIDIA GPU 和 CUDA 驱动")
        return False
    
    print(f"   - CUDA 可用: ✅")
    print(f"   - CUDA 版本: {torch.version.cuda}")
    print(f"   - GPU 数量: {torch.cuda.device_count()}")
    
    for i in range(torch.cuda.device_count()):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
        print(f"   - GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    
    return True

def check_mamba_ssm():
    """检查 mamba-ssm 安装"""
    print("\n🐍 mamba-ssm 检查:")
    
    try:
        from mamba_ssm import Mamba
        print("   - mamba-ssm 导入: ✅")
        
        # 创建测试实例
        test_model = Mamba(d_model=256, d_state=16, d_conv=4, expand=2)
        print("   - 模型创建: ✅")
        
        # 测试 CUDA
        if torch.cuda.is_available():
            test_model = test_model.cuda()
            test_input = torch.randn(2, 10, 256, device='cuda')
            
            with torch.no_grad():
                test_output = test_model(test_input)
            
            print(f"   - CUDA 测试: ✅ ({test_input.shape} -> {test_output.shape})")
        else:
            test_input = torch.randn(2, 10, 256)
            with torch.no_grad():
                test_output = test_model(test_input)
            print(f"   - CPU 测试: ✅ ({test_input.shape} -> {test_output.shape})")
        
        return True
        
    except ImportError as e:
        print(f"   - mamba-ssm 导入: ❌")
        print(f"     错误: {e}")
        print("     💡 安装命令: pip install mamba-ssm")
        return False
    except Exception as e:
        print(f"   - mamba-ssm 测试: ❌")
        print(f"     错误: {e}")
        return False

def check_project_dependencies():
    """检查项目依赖"""
    print("\n📦 项目依赖检查:")
    
    required_packages = [
        'numpy', 'opencv-python', 'pillow', 'einops', 
        'wandb', 'scipy', 'matplotlib', 'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   - {package}: ✅")
        except ImportError:
            print(f"   - {package}: ❌")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   ⚠️  缺少依赖: {', '.join(missing_packages)}")
        print(f"   💡 安装命令: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_cim_tracker():
    """检查 CIM-Tracker 模块"""
    print("\n🎯 CIM-Tracker 模块检查:")
    
    try:
        # 检查原始模块
        from models.cdiim import CDIIM
        print("   - 原始 CDIIM: ✅")
        
        # 检查 mamba-ssm 模块
        from models.cdiim_mamba_ssm import CDIIMv2, MAMBA_SSM_AVAILABLE
        print(f"   - mamba-ssm CDIIM: {'✅' if MAMBA_SSM_AVAILABLE else '❌'}")
        
        # 检查模型构建
        from models import build_model
        print("   - 模型构建函数: ✅")
        
        return True
        
    except ImportError as e:
        print(f"   - 模块导入失败: ❌")
        print(f"     错误: {e}")
        return False

def check_data_paths():
    """检查数据路径"""
    print("\n📁 数据路径检查:")
    
    import os
    
    data_paths = [
        '/data/Datasets',
        '/data/CIM_models',
        'outputs'
    ]
    
    all_exist = True
    
    for path in data_paths:
        if os.path.exists(path):
            print(f"   - {path}: ✅")
        else:
            print(f"   - {path}: ❌ (不存在)")
            all_exist = False
    
    if not all_exist:
        print("   💡 创建目录: mkdir -p /data/Datasets /data/CIM_models outputs")
    
    return all_exist

def generate_report(results):
    """生成检查报告"""
    print("\n" + "="*60)
    print("📋 兼容性检查报告")
    print("="*60)
    
    all_passed = all(results.values())
    
    for check_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{check_name:<20}: {status}")
    
    print("-" * 60)
    
    if all_passed:
        print("🎉 所有检查通过！可以使用 mamba-ssm 版本")
        print("\n🚀 下一步:")
        print("   1. 运行性能测试: python3 benchmark_mamba_ssm.py")
        print("   2. 开始训练: ./train_mamba_ssm.sh")
    else:
        print("⚠️  部分检查失败，请解决问题后重试")
        print("\n🔧 解决方案:")
        
        if not results['系统环境']:
            print("   - 在 WSL/Linux 环境中运行")
        if not results['CUDA环境']:
            print("   - 安装 NVIDIA 驱动和 CUDA")
        if not results['mamba-ssm']:
            print("   - 安装 mamba-ssm: pip install mamba-ssm")
        if not results['项目依赖']:
            print("   - 安装缺失的 Python 包")
        if not results['CIM-Tracker模块']:
            print("   - 检查项目文件完整性")
        if not results['数据路径']:
            print("   - 创建必要的数据目录")

def main():
    """主函数"""
    print("🔍 CIM-Tracker mamba-ssm 兼容性检查")
    print("="*60)
    
    # 运行所有检查
    results = {
        '系统环境': check_system(),
        'CUDA环境': check_cuda(),
        'mamba-ssm': check_mamba_ssm(),
        '项目依赖': check_project_dependencies(),
        'CIM-Tracker模块': check_cim_tracker(),
        '数据路径': check_data_paths(),
    }
    
    # 生成报告
    generate_report(results)
    
    # 返回退出码
    return 0 if all(results.values()) else 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
