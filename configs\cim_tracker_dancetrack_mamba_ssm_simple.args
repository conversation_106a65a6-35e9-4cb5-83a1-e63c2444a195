# CIM-Tracker DanceTrack 配置 - 简化测试版本
# 基于之前成功的配置

# ================== 基础设置 ==================
--output_dir
logs/cim_tracker_dancetrack_mamba_ssm_simple

--dataset_file
e2e_joint

--mot_path
/mnt/d/Projects/Datasets

# ================== 模型架构 ==================
--meta_arch
motr_mamba_ssm

--backbone
resnet50

--position_embedding
sine

--enc_layers
6

--dec_layers
6

--dim_feedforward
1024

--hidden_dim
256

--dropout
0.1

--nheads
8

--num_queries
8

# ================== CIM-Tracker 参数 ==================
--fp_ratio
0.3

--query_denoise
5

--num_id_vocabulary
50

--id_dim
256

# ================== Mamba 参数 ==================
--mamba_num_layers
3

--mamba_state_dim
12

--mamba_expand
1.5

--mamba_conv_dim
4

--use_mamba_ssm

# ================== 训练参数 ==================
--lr
2e-4

--lr_backbone
2e-5

--lr_drop
40

--epochs
50

--batch_size
2

--weight_decay
1e-4

--clip_max_norm
1.0

# ================== 数据采样（简化版本）==================
--sampler_lengths
5

--sampler_steps
5

--sample_mode
random_interval

--sample_interval
1

# ================== 损失权重 ==================
--cls_loss_coef
2.0

--bbox_loss_coef
5.0

--giou_loss_coef
2.0

--id_loss_coef
2.0

--focal_alpha
0.25

# ================== 数据增强 ==================
--random_drop
0.1

--merger_dropout
0.1

# ================== 匹配成本 ==================
--set_cost_class
2.0

--set_cost_bbox
5.0

--set_cost_giou
2.0

# ================== 保存和评估 ==================
--save_period
5

# ================== 数据路径 ==================
--det_db
/mnt/d/Projects/Datasets/CIM_models/det_db_motrv2.json

--pretrained
/mnt/d/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth

# ================== 设备设置 ==================
--device
cuda

--seed
42

# ================== 模型选项 ==================
--with_box_refine

# ================== 内存优化 ==================
--grad_frames
5
