# CIM-Tracker DanceTrack 配置 - Mamba-SSM 版本
# 基于官方 mamba-ssm 的高性能实现，适用于 WSL 环境
# 推荐配置：序列长度 10，批次大小 2

# ================== 基础设置 ==================
--output_dir
logs/cim_tracker_dancetrack_mamba_ssm

--dataset_file
e2e_dance

--mot_path
/mnt/d/Projects/Datasets

# ================== 模型架构（关键！）==================
--meta_arch
motr_mamba_ssm

--backbone
resnet50

--position_embedding
sine

--enc_layers
6

--dec_layers
6

--dim_feedforward
1024

--hidden_dim
256

--dropout
0.1

--nheads
8

--num_queries
8

# ================== CIM-Tracker 特有参数 ==================
--fp_ratio
0

--query_denoise
0.1

--num_id_vocabulary
300

--id_dim
256

# ================== Mamba 参数 ==================
--mamba_num_layers
3

--mamba_state_dim
12

--mamba_expand
1.5

--mamba_conv_dim
4

--use_mamba_ssm

# ================== 训练参数 ==================
--lr
2e-4

--lr_backbone
2e-5

--lr_drop
15

--lr_scheduler
cosine

--epochs
20

--batch_size
2

--weight_decay
1e-4

--clip_max_norm
0.01

# ================== 性能优化 ==================
--use_amp

# 暂时禁用torch.compile来测试ID映射修复
# --compile_model
# --compile_mode
# max-autotune

# ================== 数据采样（优化版本）==================
--sampler_lengths
5

--sampler_steps
5

--sample_mode
random_interval

--sample_interval
1

# ================== 损失权重 ==================
--cls_loss_coef
2.0

--bbox_loss_coef
5.0

--giou_loss_coef
2.0

--id_loss_coef
2.0

--focal_alpha
0.25

# ================== 数据增强 ==================
--random_drop
0

--merger_dropout
0.1

# ================== 匹配成本 ==================
--set_cost_class
2.0

--set_cost_bbox
5.0

--set_cost_giou
2.0

# ================== 保存和评估 ==================
--save_period
5

# ================== 数据路径（WSL 格式）==================
--det_db
/mnt/d/Projects/Datasets/CIM_models/det_db_motrv2.json

--pretrained
/mnt/d/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth

# ================== 设备设置 ==================
--device
cuda

--seed
42

# ================== 模型选项 ==================
--with_box_refine

# ================== 内存优化 ==================
--grad_frames
3

--num_workers
2
