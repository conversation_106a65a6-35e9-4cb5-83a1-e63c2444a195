# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-research. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from Deformable DETR (https://github.com/fundamentalvision/Deformable-DETR)
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------


"""
Train and eval functions used in main.py
"""
import math
import os
import sys
import time
import datetime
from typing import Iterable
import wandb # Add wandb import

import torch
from torch.amp import autocast, GradScaler
import util.misc as utils

from datasets.data_prefetcher import data_dict_to_cuda


def train_one_epoch_mot(model: torch.nn.Module, criterion: torch.nn.Module,
                    data_loader: Iterable, optimizer: torch.optim.Optimizer,
                    device: torch.device, epoch: int, max_norm: float = 0,
                    grad_frames: int = 5, use_amp: bool = False):  # 新增AMP参数
    model.train()
    # CIPT模型内部包含criterion，不需要单独设置训练模式
    if criterion is not None:
        criterion.train()
    metric_logger = utils.MetricLogger(delimiter="  ")
    metric_logger.add_meter('lr', utils.SmoothedValue(window_size=1, fmt='{value:.6f}'))
    # metric_logger.add_meter('class_error', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))    metric_logger.add_meter('grad_norm', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))
    header = 'Epoch: [{}]'.format(epoch)
    
    # ================== 自适应打印频率：前30个batch详细输出，后续每epoch输出5次 ==================
    total_batches = len(data_loader)
    target_prints_per_epoch = 5
    print_freq = max(1, total_batches // target_prints_per_epoch)

    print(f"📊 Epoch {epoch}: Total batches = {total_batches}, Print frequency = {print_freq} (will output {target_prints_per_epoch} times per epoch)")
    print(f"📝 前30个batch将详细输出，batch 10, 20, 30 会额外显示")
    # ================================================================

    # ================== 初始化 AMP GradScaler ==================
    scaler = None
    if use_amp:
        scaler = GradScaler(
            init_scale=2.**10,  # 较小的初始scale
            growth_factor=1.5,   # 较慢的增长
            backoff_factor=0.8,  # 更快的回退
            growth_interval=1000 # 较长的增长间隔
        )
        print(f"🔥 AMP enabled with GradScaler")
    else:
        print(f"📝 AMP disabled, using FP32")

    # 自定义日志输出逻辑：前30个batch详细输出
    iteration = 0
    start_time = time.time()
    end = time.time()

    for data_dict in data_loader:
        # 测量数据加载时间
        data_time = time.time() - end
        data_dict = data_dict_to_cuda(data_dict, device)

        # ================== CIPT AMP 前向传播 ==================
        # CIPT模型内部包含criterion，直接返回损失
        if use_amp:
            with autocast(device_type='cuda'):
                if hasattr(model, 'criterion') and model.criterion is not None:
                    # CIPT模型：内部计算损失
                    losses, loss_dict = model(data_dict)
                else:
                    # 传统模型：外部计算损失
                    outputs = model(data_dict)
                    loss_dict = criterion(outputs, data_dict['gt_instances'])
                    losses = sum(loss_dict[k] * criterion.weight_dict[k]
                               for k in loss_dict.keys() if k in criterion.weight_dict)
        else:
            if hasattr(model, 'criterion') and model.criterion is not None:
                # CIPT模型：内部计算损失
                losses, loss_dict = model(data_dict)
            else:
                # 传统模型：外部计算损失
                outputs = model(data_dict)
                loss_dict = criterion(outputs, data_dict['gt_instances'])
                losses = sum(loss_dict[k] * criterion.weight_dict[k]
                           for k in loss_dict.keys() if k in criterion.weight_dict)

        # 调试代码已移除 - 损失计算正常工作

        # print("iter {} after model".format(cnt-1))
        # 获取权重字典 - 适配CIPT和传统模型
        if hasattr(model, 'criterion') and model.criterion is not None:
            weight_dict = model.criterion.weight_dict
        else:
            weight_dict = criterion.weight_dict

        # ================== CIPT 损失处理逻辑 ==================
        # CIPT模型已经在内部计算了加权损失，传统模型需要外部计算
        if hasattr(model, 'criterion') and model.criterion is not None:
            # CIPT模型：losses已经是加权后的总损失
            full_losses_reduced = losses
        else:
            # 传统模型：使用原有的长序列训练逻辑
            # 仅对最后 'grad_frames' 帧的损失进行反向传播
            total_frames = len(data_dict['imgs'])
            start_grad_frame = max(0, total_frames - grad_frames)

            # 收集需要反向传播的损失
            grad_losses = []
            for k, v in loss_dict.items():
                if k in weight_dict:
                    # 从损失名称中解析帧索引, e.g., 'frame_5_loss_id' -> 5
                    try:
                        frame_idx = int(k.split('_')[1])
                        if frame_idx >= start_grad_frame:
                            grad_losses.append(v * weight_dict[k])
                    except (ValueError, IndexError):
                        # 如果损失名称不符合'frame_x_...'格式，默认全部计算梯度
                        grad_losses.append(v * weight_dict[k])

            # 计算总的梯度损失
            if grad_losses:
                losses = sum(grad_losses)
            else:
                # 如果没有需要梯度的损失，创建一个零张量
                losses = torch.tensor(0.0, device=next(iter(loss_dict.values())).device if loss_dict else 'cpu', requires_grad=True)

            # 为了日志记录，我们仍然计算所有帧的总损失值
            valid_losses = [loss_dict[k] * weight_dict[k] for k in loss_dict.keys() if k in weight_dict]
            if valid_losses:
                full_losses_reduced = sum(valid_losses)
            else:
                # 如果没有有效损失，创建一个零张量
                full_losses_reduced = torch.tensor(0.0, device=next(iter(loss_dict.values())).device if loss_dict else 'cpu')
        # ======================================================

        # reduce losses over all GPUs for logging purposes
        loss_dict_reduced = utils.reduce_dict(loss_dict)
        # loss_dict_reduced_unscaled = {f'{k}_unscaled': v
        #                               for k, v in loss_dict_reduced.items()}
        loss_dict_reduced_scaled = {k: v * weight_dict[k]
                                    for k, v in loss_dict_reduced.items() if k in weight_dict}
        losses_reduced_scaled = sum(loss_dict_reduced_scaled.values())

        loss_value = full_losses_reduced.item()  # 日志记录完整损失

        if not math.isfinite(loss_value):
            print("Loss is {}, stopping training".format(loss_value))
            print(loss_dict_reduced)
            sys.exit(1)

        optimizer.zero_grad()

        # ================== AMP 反向传播 ==================
        if use_amp:
            scaler.scale(losses).backward()  # 只反向传播部分损失
        else:
            losses.backward()

        if max_norm > 0:
            if use_amp:
                # AMP 梯度裁剪需要先 unscale
                scaler.unscale_(optimizer)
                grad_total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
            else:
                grad_total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
        else:
            if use_amp:
                scaler.unscale_(optimizer)
            grad_total_norm = utils.get_total_grad_norm(model.parameters(), max_norm)

        # AMP 优化器步骤
        if use_amp:
            scaler.step(optimizer)
            scaler.update()
        else:
            optimizer.step()

        # 测量总的batch处理时间
        batch_time = time.time() - end

        # 更新metric_logger
        metric_logger.update(loss=loss_value, **loss_dict_reduced_scaled)
        metric_logger.update(lr=optimizer.param_groups[0]["lr"])
        metric_logger.update(grad_norm=grad_total_norm)
        metric_logger.update(time=batch_time, data=data_time)
        
        # Log metrics to WandB
        if utils.is_main_process():
            log_data = {
                'train_loss': loss_value,
                'lr': optimizer.param_groups[0]["lr"],
                'grad_norm': grad_total_norm.item() if isinstance(grad_total_norm, torch.Tensor) else grad_total_norm,
                **{f'train_{k}': v.item() if isinstance(v, torch.Tensor) else v for k, v in loss_dict_reduced_scaled.items()}
            }
            wandb.log(log_data, step=epoch * len(data_loader) + iteration)

        # ================== 自定义日志输出 ==================
        # 只输出特定batch：0, 10, 20, 30，然后按正常频率输出
        should_print = (
            iteration in [0, 10, 20, 30] or  # 特定batch
            (iteration > 30 and iteration % print_freq == 0)  # 30之后按正常频率
        )

        if should_print:
            # 计算ETA - 使用实际时间数据
            if iteration > 3:  # 等待几个数据点
                try:
                    avg_time = metric_logger.meters['time'].global_avg
                    eta_seconds = avg_time * (total_batches - iteration)
                    eta_string = str(datetime.timedelta(seconds=int(eta_seconds)))
                except:
                    # 如果metric失败，使用当前batch时间估算
                    current_time = time.time() - start_time
                    avg_time_per_batch = current_time / (iteration + 1)
                    eta_seconds = avg_time_per_batch * (total_batches - iteration)
                    eta_string = str(datetime.timedelta(seconds=int(eta_seconds)))
            else:
                # 前几个batch使用基于当前进度的估算
                if iteration > 0:
                    current_time = time.time() - start_time
                    avg_time_per_batch = current_time / (iteration + 1)
                    eta_seconds = avg_time_per_batch * (total_batches - iteration)
                    eta_string = str(datetime.timedelta(seconds=int(eta_seconds)))
                else:
                    eta_string = "N/A"

            # 构建日志信息
            log_msg = f"{header}  [{iteration:>5d}/{total_batches}]  eta: {eta_string}"
            log_msg += f"  lr: {optimizer.param_groups[0]['lr']:.6f}"
            # 修复loss_value的格式化
            if isinstance(loss_value, torch.Tensor):
                log_msg += f"  loss: {loss_value.item():.4f}"
            else:
                log_msg += f"  loss: {loss_value:.4f}"

            # 添加各项损失（当前值）- 修复张量格式化问题
            for k, v in loss_dict_reduced_scaled.items():
                if isinstance(v, torch.Tensor):
                    log_msg += f"  {k}: {v.item():.4f}"
                else:
                    log_msg += f"  {k}: {v:.4f}"

            # 修复grad_total_norm的格式化
            if isinstance(grad_total_norm, torch.Tensor):
                log_msg += f"  grad_norm: {grad_total_norm.item():.4f}"
            else:
                log_msg += f"  grad_norm: {grad_total_norm:.4f}"

            # 添加时间和数据统计
            try:
                time_val = metric_logger.meters['time'].value if 'time' in metric_logger.meters else 0.0
                data_val = metric_logger.meters['data'].value if 'data' in metric_logger.meters else 0.0
                log_msg += f"  time: {time_val:.4f}"
                log_msg += f"  data: {data_val:.4f}"
            except:
                log_msg += f"  time: N/A  data: N/A"

            log_msg += f"  max mem: {torch.cuda.max_memory_allocated() // 1024 // 1024}"

            print(log_msg)

        iteration += 1
        end = time.time()  # 为下一次迭代准备


    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger)
    
    # Log epoch-level averaged stats to WandB
    if utils.is_main_process():
        epoch_log_data = {f'epoch_avg_train_{k}': meter.global_avg for k, meter in metric_logger.meters.items() if k not in ['lr', 'grad_norm']}
        epoch_log_data['epoch'] = epoch
        wandb.log(epoch_log_data)
        
    return {k: meter.global_avg for k, meter in metric_logger.meters.items()}
