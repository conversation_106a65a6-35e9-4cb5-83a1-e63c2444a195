#!/usr/bin/env python3
"""
CIM-Tracker 性能对比测试
比较原始实现 vs mamba-ssm 实现的性能差异
"""

import torch
import time
import sys
import os
import gc
import numpy as np
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_gpu_memory():
    """获取GPU内存使用情况"""
    if torch.cuda.is_available():
        return {
            'allocated': torch.cuda.memory_allocated() / 1024**3,  # GB
            'reserved': torch.cuda.memory_reserved() / 1024**3,    # GB
            'max_allocated': torch.cuda.max_memory_allocated() / 1024**3,  # GB
        }
    return {'allocated': 0, 'reserved': 0, 'max_allocated': 0}

def reset_gpu_memory():
    """重置GPU内存统计"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
        gc.collect()

def benchmark_mamba_implementation(implementation_name: str, model_class, 
                                 batch_size: int = 4, seq_length: int = 10, 
                                 d_model: int = 256, num_runs: int = 5):
    """
    测试特定 Mamba 实现的性能
    
    Args:
        implementation_name: 实现名称
        model_class: 模型类
        batch_size: 批次大小
        seq_length: 序列长度
        d_model: 模型维度
        num_runs: 运行次数
    
    Returns:
        性能统计字典
    """
    print(f"\n{'='*60}")
    print(f"🧪 测试 {implementation_name}")
    print(f"   - batch_size: {batch_size}")
    print(f"   - seq_length: {seq_length}")
    print(f"   - d_model: {d_model}")
    print(f"   - num_runs: {num_runs}")
    print(f"{'='*60}")
    
    reset_gpu_memory()
    
    try:
        # 创建模型
        print("🤖 创建模型...")
        if implementation_name == "mamba-ssm":
            from mamba_ssm import Mamba
            model = Mamba(
                d_model=d_model,
                d_state=16,
                d_conv=4,
                expand=2
            )
        else:
            # 原始实现
            model = model_class(
                d_model=d_model,
                mamba_state_dim=16,
                mamba_expand=2,
                mamba_conv_dim=4
            )
        
        model = model.cuda()
        model.train()
        
        memory_after_model = get_gpu_memory()
        print(f"📊 模型加载后显存: {memory_after_model['allocated']:.2f}GB")
        
        # 创建测试数据
        print("📦 创建测试数据...")
        if implementation_name == "mamba-ssm":
            # mamba-ssm 期望 (batch, seq_len, dim)
            test_input = torch.randn(batch_size, seq_length, d_model, device='cuda')
        else:
            # 原始实现可能期望不同的输入格式
            test_input = torch.randn(batch_size, seq_length, d_model, device='cuda')
        
        memory_after_data = get_gpu_memory()
        print(f"📊 数据加载后显存: {memory_after_data['allocated']:.2f}GB")
        
        # 预热运行
        print("🔥 预热运行...")
        with torch.no_grad():
            for _ in range(3):
                _ = model(test_input)
        
        torch.cuda.synchronize()
        
        # 性能测试 - 前向传播
        print("⚡ 前向传播性能测试...")
        forward_times = []
        
        for i in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            with torch.no_grad():
                output = model(test_input)
            
            torch.cuda.synchronize()
            forward_time = time.time() - start_time
            forward_times.append(forward_time)
            
            print(f"   Run {i+1}: {forward_time:.4f}s")
        
        # 性能测试 - 反向传播
        print("🔄 反向传播性能测试...")
        backward_times = []
        
        for i in range(num_runs):
            model.zero_grad()
            
            torch.cuda.synchronize()
            start_time = time.time()
            
            output = model(test_input)
            loss = output.sum()
            loss.backward()
            
            torch.cuda.synchronize()
            backward_time = time.time() - start_time
            backward_times.append(backward_time)
            
            print(f"   Run {i+1}: {backward_time:.4f}s")
        
        # 计算统计信息
        peak_memory = get_gpu_memory()
        
        forward_mean = np.mean(forward_times)
        forward_std = np.std(forward_times)
        backward_mean = np.mean(backward_times)
        backward_std = np.std(backward_times)
        
        # 计算吞吐量
        throughput = batch_size * seq_length / forward_mean  # tokens/second
        
        results = {
            'implementation': implementation_name,
            'batch_size': batch_size,
            'seq_length': seq_length,
            'd_model': d_model,
            'forward_time_mean': forward_mean,
            'forward_time_std': forward_std,
            'backward_time_mean': backward_mean,
            'backward_time_std': backward_std,
            'total_time_mean': forward_mean + backward_mean,
            'peak_memory_gb': peak_memory['max_allocated'],
            'throughput_tokens_per_sec': throughput,
            'success': True
        }
        
        print(f"\n📋 {implementation_name} 结果:")
        print(f"   - 前向传播: {forward_mean:.4f}±{forward_std:.4f}s")
        print(f"   - 反向传播: {backward_mean:.4f}±{backward_std:.4f}s")
        print(f"   - 总时间: {forward_mean + backward_mean:.4f}s")
        print(f"   - 峰值显存: {peak_memory['max_allocated']:.2f}GB")
        print(f"   - 吞吐量: {throughput:.1f} tokens/s")
        
        return results
        
    except Exception as e:
        print(f"❌ {implementation_name} 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            'implementation': implementation_name,
            'success': False,
            'error': str(e)
        }

def run_comprehensive_benchmark():
    """运行全面的性能对比测试"""
    print("🚀 CIM-Tracker Mamba 实现性能对比测试")
    print(f"🔧 PyTorch版本: {torch.__version__}")
    print(f"🎮 CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"🎮 GPU: {torch.cuda.get_device_name()}")
        print(f"💾 总显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 测试配置
    test_configs = [
        # (batch_size, seq_length, d_model)
        (2, 5, 256),    # 小规模测试
        (4, 10, 256),   # 中等规模测试
        (8, 20, 256),   # 大规模测试 (如果显存允许)
    ]
    
    all_results = []
    
    for batch_size, seq_length, d_model in test_configs:
        print(f"\n{'='*80}")
        print(f"📊 测试配置: batch_size={batch_size}, seq_length={seq_length}, d_model={d_model}")
        print(f"{'='*80}")
        
        # 测试 mamba-ssm 实现
        try:
            from mamba_ssm import Mamba
            mamba_ssm_results = benchmark_mamba_implementation(
                "mamba-ssm", Mamba, batch_size, seq_length, d_model
            )
            all_results.append(mamba_ssm_results)
        except ImportError:
            print("❌ mamba-ssm 不可用，跳过测试")
            mamba_ssm_results = {'implementation': 'mamba-ssm', 'success': False, 'error': 'Not available'}
            all_results.append(mamba_ssm_results)
        
        # 测试原始实现
        try:
            from models.cife import MambaBlock
            original_results = benchmark_mamba_implementation(
                "Original", MambaBlock, batch_size, seq_length, d_model
            )
            all_results.append(original_results)
        except Exception as e:
            print(f"❌ 原始实现测试失败: {e}")
            original_results = {'implementation': 'Original', 'success': False, 'error': str(e)}
            all_results.append(original_results)
        
        # 清理显存
        reset_gpu_memory()
        time.sleep(2)
    
    # 生成对比报告
    print("\n" + "="*100)
    print("📊 性能对比测试结果总结")
    print("="*100)
    
    successful_results = [r for r in all_results if r.get('success', False)]
    
    if successful_results:
        print(f"{'实现':<15} {'配置':<20} {'前向(s)':<10} {'反向(s)':<10} {'总时间(s)':<10} {'显存(GB)':<10} {'吞吐量':<15}")
        print("-" * 100)
        
        for result in successful_results:
            config_str = f"{result['batch_size']}x{result['seq_length']}x{result['d_model']}"
            forward_str = f"{result['forward_time_mean']:.4f}"
            backward_str = f"{result['backward_time_mean']:.4f}"
            total_str = f"{result['total_time_mean']:.4f}"
            memory_str = f"{result['peak_memory_gb']:.2f}"
            throughput_str = f"{result['throughput_tokens_per_sec']:.1f} tok/s"
            
            print(f"{result['implementation']:<15} {config_str:<20} {forward_str:<10} {backward_str:<10} {total_str:<10} {memory_str:<10} {throughput_str:<15}")
        
        # 计算加速比
        print("\n🚀 性能提升分析:")
        
        # 按配置分组
        configs = {}
        for result in successful_results:
            config_key = (result['batch_size'], result['seq_length'], result['d_model'])
            if config_key not in configs:
                configs[config_key] = {}
            configs[config_key][result['implementation']] = result
        
        for config_key, implementations in configs.items():
            if 'mamba-ssm' in implementations and 'Original' in implementations:
                mamba_ssm = implementations['mamba-ssm']
                original = implementations['Original']
                
                speedup = original['total_time_mean'] / mamba_ssm['total_time_mean']
                memory_ratio = original['peak_memory_gb'] / mamba_ssm['peak_memory_gb']
                throughput_ratio = mamba_ssm['throughput_tokens_per_sec'] / original['throughput_tokens_per_sec']
                
                print(f"   配置 {config_key}:")
                print(f"     - 速度提升: {speedup:.2f}x")
                print(f"     - 显存比例: {memory_ratio:.2f}x")
                print(f"     - 吞吐量提升: {throughput_ratio:.2f}x")
    
    else:
        print("❌ 没有成功的测试结果")
    
    # 保存结果
    import json
    with open('benchmark_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n💾 详细结果已保存到: benchmark_results.json")
    
    return all_results

def main():
    """主函数"""
    try:
        results = run_comprehensive_benchmark()
        
        # 检查是否有成功的 mamba-ssm 结果
        mamba_ssm_success = any(
            r.get('implementation') == 'mamba-ssm' and r.get('success', False) 
            for r in results
        )
        
        if mamba_ssm_success:
            print("\n🎉 mamba-ssm 性能测试成功！")
            print("💡 建议:")
            print("   1. 在 WSL/Linux 环境中使用 mamba-ssm 实现")
            print("   2. 根据测试结果调整 batch_size 和 seq_length")
            print("   3. 监控训练过程中的显存使用")
        else:
            print("\n⚠️  mamba-ssm 测试失败或不可用")
            print("💡 建议:")
            print("   1. 确保在 WSL/Linux 环境中运行")
            print("   2. 检查 mamba-ssm 安装: pip install mamba-ssm")
            print("   3. 验证 CUDA 环境配置")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
